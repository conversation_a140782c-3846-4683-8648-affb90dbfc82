/*
 * Python 3.8+ Grammar for ANTLR4
 * Simplified grammar focusing on core Python features
 * Supports expressions, statements, functions, classes, and modern syntax
 */

grammar Python3;

// Parser Rules

// Top-level constructs
file_input: (NEWLINE | stmt)* EOF;

// Statements
stmt: simple_stmt | compound_stmt;

simple_stmt: small_stmt (SEMI_COLON small_stmt)* SEMI_COLON? NEWLINE?;

small_stmt: expr_stmt | pass_stmt | del_stmt | return_stmt | import_stmt | global_stmt | nonlocal_stmt | assert_stmt | break_stmt | continue_stmt | raise_stmt;

expr_stmt: expr (ASSIGN expr | augassign expr | WALRUS expr)*;

augassign: ADD_ASSIGN | SUB_ASSIGN | MULT_ASSIGN | AT_ASSIGN | DIV_ASSIGN | MOD_ASSIGN | AND_ASSIGN | OR_ASSIGN | XOR_ASSIGN | LEFT_SHIFT_ASSIGN | RIGHT_SHIFT_ASSIGN | POWER_ASSIGN | IDIV_ASSIGN;

pass_stmt: PASS;

del_stmt: DEL expr;

return_stmt: RETURN expr?;

import_stmt: IMPORT dotted_name (AS NAME)? | FROM dotted_name IMPORT (STAR | import_as_names);

import_as_names: import_as_name (COMMA import_as_name)*;

import_as_name: NAME (AS NAME)?;

global_stmt: GLOBAL NAME (COMMA NAME)*;

nonlocal_stmt: NONLOCAL NAME (COMMA NAME)*;

assert_stmt: ASSERT expr (COMMA expr)?;

break_stmt: BREAK;

continue_stmt: CONTINUE;

raise_stmt: RAISE (expr (FROM expr)?)?;

compound_stmt: if_stmt | while_stmt | for_stmt | try_stmt | with_stmt | funcdef | classdef;

if_stmt: IF expr COLON suite (ELIF expr COLON suite)* (ELSE COLON suite)?;

while_stmt: WHILE expr COLON suite (ELSE COLON suite)?;

for_stmt: FOR NAME IN expr COLON suite (ELSE COLON suite)?;

try_stmt: TRY COLON suite
          (except_clause COLON suite)*
          (ELSE COLON suite)?
          (FINALLY COLON suite)?;

except_clause: EXCEPT (expr (AS NAME)?)?;

with_stmt: WITH with_item (COMMA with_item)* COLON suite;

with_item: expr (AS NAME)?;

funcdef: DEF NAME OPEN_PAREN parameters? CLOSE_PAREN (ARROW expr)? COLON suite;

parameters: parameter_list;

parameter_list: parameter (COMMA parameter)*;

parameter: NAME (COLON expr)? (ASSIGN expr)?
         | STAR NAME
         | POWER NAME
         | DIV;

classdef: CLASS NAME (OPEN_PAREN arglist? CLOSE_PAREN)? COLON suite;

suite: simple_stmt | NEWLINE stmt+;

// Expressions
expr: conditional_expr | lambda_expr;

conditional_expr: or_expr (IF or_expr ELSE conditional_expr)?;

lambda_expr: LAMBDA parameters? COLON expr;

or_expr: and_expr (OR and_expr)*;

and_expr: not_expr (AND not_expr)*;

not_expr: NOT not_expr | comparison;

comparison: bitwise_or (comp_op bitwise_or)*;

comp_op: LESS_THAN | GREATER_THAN | EQUALS | GT_EQ | LT_EQ | NOT_EQ_2 | IN | NOT IN | IS | IS NOT;

bitwise_or: bitwise_xor (OR_OP bitwise_xor)*;

bitwise_xor: bitwise_and (XOR bitwise_and)*;

bitwise_and: shift_expr (AND_OP shift_expr)*;

shift_expr: arith_expr ((LEFT_SHIFT | RIGHT_SHIFT) arith_expr)*;

arith_expr: term ((ADD | MINUS) term)*;

term: factor ((STAR | AT | DIV | MOD | IDIV) factor)*;

factor: (ADD | MINUS | NOT_OP) factor | power;

power: atom_expr (POWER factor)?;

atom_expr: atom trailer*;

atom: NAME | NUMBER | STRING | TRUE | FALSE | NONE | ELLIPSIS |
      OPEN_PAREN NEWLINE* (yield_expr | testlist_comp)? NEWLINE* CLOSE_PAREN |
      OPEN_BRACK NEWLINE* (testlist_comp)? NEWLINE* CLOSE_BRACK |
      OPEN_BRACE NEWLINE* (dictorsetmaker)? NEWLINE* CLOSE_BRACE;

testlist_comp: (expr | star_expr) (comp_for | (COMMA NEWLINE* (expr | star_expr))* COMMA? NEWLINE*);

star_expr: STAR expr;

comp_for: FOR expr IN or_expr (comp_iter)?;

comp_iter: comp_for | comp_if;

comp_if: IF expr (comp_iter)?;

dictorsetmaker: ((expr COLON expr | POWER expr) (comp_for | (COMMA NEWLINE* (expr COLON expr | POWER expr))* COMMA? NEWLINE*)) |
                (expr (comp_for | (COMMA NEWLINE* expr)* COMMA? NEWLINE*));

yield_expr: YIELD (FROM expr | expr_list?);

trailer: OPEN_PAREN arglist? CLOSE_PAREN | OPEN_BRACK subscriptlist CLOSE_BRACK | DOT NAME;

subscriptlist: subscript (COMMA subscript)* COMMA?;

subscript: expr | expr? COLON expr? (COLON expr?)?;

arglist: argument (COMMA argument)* COMMA?;

argument: expr (comp_for)? | expr ASSIGN expr | STAR expr | POWER expr;

expr_list: expr (COMMA expr)* COMMA?;

dotted_name: NAME (DOT NAME)*;

// Lexer Rules

// Keywords (Python 3.8+ keywords)
FALSE: 'False';
NONE: 'None';
TRUE: 'True';
AND: 'and';
AS: 'as';
ASSERT: 'assert';
ASYNC: 'async';
AWAIT: 'await';
BREAK: 'break';
CLASS: 'class';
CONTINUE: 'continue';
DEF: 'def';
DEL: 'del';
ELIF: 'elif';
ELSE: 'else';
EXCEPT: 'except';
FINALLY: 'finally';
FOR: 'for';
FROM: 'from';
GLOBAL: 'global';
IF: 'if';
IMPORT: 'import';
IN: 'in';
IS: 'is';
LAMBDA: 'lambda';
NONLOCAL: 'nonlocal';
NOT: 'not';
OR: 'or';
PASS: 'pass';
RAISE: 'raise';
RETURN: 'return';
TRY: 'try';
WHILE: 'while';
WITH: 'with';
YIELD: 'yield';

// Operators and Delimiters
DOT: '.';
ELLIPSIS: '...';
STAR: '*';
OPEN_PAREN: '(';
CLOSE_PAREN: ')';
COMMA: ',';
COLON: ':';
SEMI_COLON: ';';
POWER: '**';
ASSIGN: '=';
OPEN_BRACK: '[';
CLOSE_BRACK: ']';
OR_OP: '|';
XOR: '^';
AND_OP: '&';
LEFT_SHIFT: '<<';
RIGHT_SHIFT: '>>';
ADD: '+';
MINUS: '-';
DIV: '/';
MOD: '%';
IDIV: '//';
NOT_OP: '~';
OPEN_BRACE: '{';
CLOSE_BRACE: '}';
LESS_THAN: '<';
GREATER_THAN: '>';
EQUALS: '==';
GT_EQ: '>=';
LT_EQ: '<=';
NOT_EQ_1: '<>';
NOT_EQ_2: '!=';
AT: '@';
ARROW: '->';
ADD_ASSIGN: '+=';
SUB_ASSIGN: '-=';
MULT_ASSIGN: '*=';
AT_ASSIGN: '@=';
DIV_ASSIGN: '/=';
MOD_ASSIGN: '%=';
AND_ASSIGN: '&=';
OR_ASSIGN: '|=';
XOR_ASSIGN: '^=';
LEFT_SHIFT_ASSIGN: '<<=';
RIGHT_SHIFT_ASSIGN: '>>=';
POWER_ASSIGN: '**=';
IDIV_ASSIGN: '//=';
WALRUS: ':=';  // Walrus operator (Python 3.8+)

// Literals
STRING: STRING_LITERAL;
NUMBER: INTEGER | FLOAT_NUMBER | IMAG_NUMBER;
INTEGER: DECIMAL_INTEGER | OCT_INTEGER | HEX_INTEGER | BIN_INTEGER;

// String literals (including f-strings)
fragment STRING_LITERAL:
    [fF] [rR]? (F_SHORT_STRING | F_LONG_STRING) |
    [rR]? [fF] (F_SHORT_STRING | F_LONG_STRING) |
    [rR]? (SHORT_STRING | LONG_STRING);

fragment SHORT_STRING: '\'' SHORT_STRING_ITEM_SINGLE* '\'' | '"' SHORT_STRING_ITEM_DOUBLE* '"';
fragment LONG_STRING: '\'\'\'' LONG_STRING_ITEM* '\'\'\'' | '"""' LONG_STRING_ITEM* '"""';

fragment F_SHORT_STRING: '\'' F_SHORT_STRING_ITEM_SINGLE* '\'' | '"' F_SHORT_STRING_ITEM_DOUBLE* '"';
fragment F_LONG_STRING: '\'\'\'' F_LONG_STRING_ITEM* '\'\'\'' | '"""' F_LONG_STRING_ITEM* '"""';

fragment SHORT_STRING_ITEM_SINGLE: SHORT_STRING_CHAR_SINGLE | STRING_ESCAPE_SEQ;
fragment SHORT_STRING_ITEM_DOUBLE: SHORT_STRING_CHAR_DOUBLE | STRING_ESCAPE_SEQ;
fragment LONG_STRING_ITEM: LONG_STRING_CHAR | STRING_ESCAPE_SEQ;

fragment F_SHORT_STRING_ITEM_SINGLE: F_SHORT_STRING_CHAR_SINGLE | F_STRING_ESCAPE_SEQ | F_EXPRESSION;
fragment F_SHORT_STRING_ITEM_DOUBLE: F_SHORT_STRING_CHAR_DOUBLE | F_STRING_ESCAPE_SEQ | F_EXPRESSION;
fragment F_LONG_STRING_ITEM: F_LONG_STRING_CHAR | F_STRING_ESCAPE_SEQ | F_EXPRESSION;

fragment SHORT_STRING_CHAR_SINGLE: ~[\\\r\n'];
fragment SHORT_STRING_CHAR_DOUBLE: ~[\\\r\n"];
fragment LONG_STRING_CHAR: ~[\\];

fragment F_SHORT_STRING_CHAR_SINGLE: ~[\\\r\n'{];
fragment F_SHORT_STRING_CHAR_DOUBLE: ~[\\\r\n"{];
fragment F_LONG_STRING_CHAR: ~[\\{];

fragment STRING_ESCAPE_SEQ: '\\' .;
fragment F_STRING_ESCAPE_SEQ: '\\' .;

fragment F_EXPRESSION: '{' F_EXPR_CONTENT '}';
fragment F_EXPR_CONTENT: ~[{}]+;

// Numeric literals
fragment DECIMAL_INTEGER: NON_ZERO_DIGIT DIGIT* | '0'+;
fragment OCT_INTEGER: '0' [oO] OCT_DIGIT+;
fragment HEX_INTEGER: '0' [xX] HEX_DIGIT+;
fragment BIN_INTEGER: '0' [bB] BIN_DIGIT+;

fragment NON_ZERO_DIGIT: [1-9];
fragment DIGIT: [0-9];
fragment OCT_DIGIT: [0-7];
fragment HEX_DIGIT: [0-9a-fA-F];
fragment BIN_DIGIT: [01];

fragment FLOAT_NUMBER: POINT_FLOAT | EXPONENT_FLOAT;
fragment POINT_FLOAT: INT_PART? FRACTION | INT_PART '.';
fragment EXPONENT_FLOAT: (INT_PART | POINT_FLOAT) EXPONENT;
fragment INT_PART: DIGIT+;
fragment FRACTION: '.' DIGIT+;
fragment EXPONENT: [eE] [+-]? DIGIT+;

fragment IMAG_NUMBER: (FLOAT_NUMBER | INT_PART) [jJ];

// Identifiers
NAME: ID_START ID_CONTINUE*;

fragment ID_START: [a-zA-Z_];
fragment ID_CONTINUE: [a-zA-Z0-9_];

// Whitespace and comments
NEWLINE: ('\r'? '\n' | '\r') SPACES?;
WS: [ \t]+ -> skip;
COMMENT: '#' ~[\r\n]* -> skip;

// Indentation tokens (removed for now - will be handled differently)
// INDENT and DEDENT will be handled by custom lexer logic later

// Special tokens
SPACES: [ \t]+;

// Error handling
ErrorChar: .;
