package au.com.allianceautomation.iython;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * Test class for multiline tuple parsing and tuple functionality.
 * Tests various tuple scenarios including single-line, multiline, nested, and edge cases.
 */
public class MultilineTupleTest {
    
    private PythonExecutor executor;
    
    @BeforeEach
    void setUp() {
        executor = new PythonExecutor();
    }
    
    @Test
    @DisplayName("Test simple tuple creation")
    void testSimpleTuple() throws PythonExecutionException {
        String code = "t = (1, 2, 3)";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        // Currently this will fail because tuples are parsed as lists
        // After fix, this should be a tuple (immutable list in Java representation)
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(3, tuple.size());
        assertEquals(1L, tuple.get(0));
        assertEquals(2L, tuple.get(1));
        assertEquals(3L, tuple.get(2));
    }
    
    @Test
    @DisplayName("Test empty tuple creation")
    void testEmptyTuple() throws PythonExecutionException {
        String code = "t = ()";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(0, tuple.size());
    }
    
    @Test
    @DisplayName("Test single element tuple (with trailing comma)")
    void testSingleElementTuple() throws PythonExecutionException {
        String code = "t = (42,)";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(1, tuple.size());
        assertEquals(42L, tuple.get(0));
    }
    
    @Test
    @DisplayName("Test tuple without parentheses")
    void testTupleWithoutParentheses() throws PythonExecutionException {
        String code = "t = 1, 2, 3";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(3, tuple.size());
        assertEquals(1L, tuple.get(0));
        assertEquals(2L, tuple.get(1));
        assertEquals(3L, tuple.get(2));
    }
    
    @Test
    @DisplayName("Test multiline tuple with parentheses")
    void testMultilineTupleWithParentheses() throws PythonExecutionException {
        String code = """
            t = (
                1,
                2,
                3
            )
            """;
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(3, tuple.size());
        assertEquals(1L, tuple.get(0));
        assertEquals(2L, tuple.get(1));
        assertEquals(3L, tuple.get(2));
    }
    
    @Test
    @DisplayName("Test multiline tuple with trailing comma")
    void testMultilineTupleWithTrailingComma() throws PythonExecutionException {
        String code = """
            t = (
                "first",
                "second",
                "third",
            )
            """;
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(3, tuple.size());
        assertEquals("first", tuple.get(0));
        assertEquals("second", tuple.get(1));
        assertEquals("third", tuple.get(2));
    }
    
    @Test
    @DisplayName("Test nested tuples")
    void testNestedTuples() throws PythonExecutionException {
        String code = "t = ((1, 2), (3, 4), (5, 6))";
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(3, tuple.size());
        
        // Check nested tuples
        assertTrue(tuple.get(0) instanceof List);
        assertTrue(tuple.get(1) instanceof List);
        assertTrue(tuple.get(2) instanceof List);
        
        List<?> nested1 = (List<?>) tuple.get(0);
        assertEquals(2, nested1.size());
        assertEquals(1L, nested1.get(0));
        assertEquals(2L, nested1.get(1));
    }
    
    @Test
    @DisplayName("Test multiline nested tuples")
    void testMultilineNestedTuples() throws PythonExecutionException {
        String code = """
            t = (
                (1, 2),
                (
                    3,
                    4
                ),
                (5, 6)
            )
            """;
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(3, tuple.size());
        
        // Check nested tuples
        assertTrue(tuple.get(0) instanceof List);
        assertTrue(tuple.get(1) instanceof List);
        assertTrue(tuple.get(2) instanceof List);
    }
    
    @Test
    @DisplayName("Test tuple with mixed types")
    void testTupleWithMixedTypes() throws PythonExecutionException {
        String code = """
            t = (
                42,
                "hello",
                True,
                None,
                [1, 2, 3]
            )
            """;
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(t)", "t");
        assertTrue(result instanceof List);
        List<?> tuple = (List<?>) result;
        assertEquals(5, tuple.size());
        assertEquals(42L, tuple.get(0));
        assertEquals("hello", tuple.get(1));
        assertEquals(true, tuple.get(2));
        assertEquals(null, tuple.get(3));
        assertTrue(tuple.get(4) instanceof List);
    }
    
    @Test
    @DisplayName("Test tuple unpacking")
    void testTupleUnpacking() throws PythonExecutionException {
        String code = """
            t = (1, 2, 3)
            a, b, c = t
            """;
        executor.executeCode(code);

        Object resultA = executor.executeAndGetVariable("print(a)", "a");
        Object resultB = executor.executeAndGetVariable("print(b)", "b");
        Object resultC = executor.executeAndGetVariable("print(c)", "c");
        
        assertEquals(1L, resultA);
        assertEquals(2L, resultB);
        assertEquals(3L, resultC);
    }
    
    @Test
    @DisplayName("Test parentheses without comma (should not be tuple)")
    void testParenthesesWithoutComma() throws PythonExecutionException {
        String code = "x = (42)";  // This should be just 42, not a tuple
        executor.executeCode(code);

        Object result = executor.executeAndGetVariable("print(x)", "x");
        assertEquals(42L, result);  // Should be a number, not a list/tuple
    }
}
