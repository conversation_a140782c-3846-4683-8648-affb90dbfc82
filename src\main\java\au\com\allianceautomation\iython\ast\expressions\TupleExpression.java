package au.com.allianceautomation.iython.ast.expressions;

import au.com.allianceautomation.iython.ast.ASTVisitor;
import java.util.List;

/**
 * AST node representing tuple literals and tuple expressions.
 * Represents expressions like: (1, 2, 3), (x,), or ()
 * 
 * Tuples are immutable sequences in Python, distinguished from lists by:
 * - Using parentheses instead of square brackets
 * - Being immutable (cannot be modified after creation)
 * - Single element tuples require a trailing comma: (x,)
 * - Empty tuples: ()
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class TupleExpression extends Expression {
    private final List<Expression> elements;
    
    public TupleExpression(int line, int column, List<Expression> elements) {
        super(line, column);
        this.elements = elements;
    }
    
    public List<Expression> getElements() { 
        return elements; 
    }
    
    @Override
    public <T> T accept(ASTVisitor<T> visitor) {
        return visitor.visitTuple(this);
    }
    
    @Override
    public String toString() {
        return "TupleExpression(" + elements + ")";
    }
}
